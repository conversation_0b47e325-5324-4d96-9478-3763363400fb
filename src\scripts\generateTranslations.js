const json5 = require("json5");
const fs = require("fs");
const request = require("request");
const parseString = require("xml2js").parseString;
const path = require("path");

// Configuration
const MESSAGES_DIR = "./messages";
const LANGUAGES = [
  { lang: "en", code: "en-US" }, // Master language (American English)
  { lang: "uk", code: "en-GB" }, // British English
  { lang: "da", code: "da" }, // Danish
  { lang: "br", code: "pt-BR" }, // Brazilian Portuguese
  { lang: "nb", code: "nb-NO" }, // Norwegian
  { lang: "sv", code: "sv" }, // Swedish
];

const MASTER_INDEX = 0;

// Language code mapping for Microsoft Translator API
const getTranslatorCode = (locale) => {
  const codeMap = {
    en: "en-US", // American English
    uk: "en-GB", // British English
    da: "da", // Danish
    br: "pt-BR", // Brazilian Portuguese
    nb: "nb-NO", // Norwegian
    sv: "sv", // Swedish
  };
  return codeMap[locale] || "en-US";
};

// British English specific transformations
const applyBritishTransformations = (text) => {
  const americanToBritish = {
    color: "colour",
    flavor: "flavour",
    behavior: "behaviour",
    labor: "labour",
    neighbor: "neighbour",
    center: "centre",
    meter: "metre",
    theater: "theatre",
    catalog: "catalogue",
    program: "programme",
    practice: "practise",
    license: "licence",
    customize: "customise",
    realize: "realise",
    analyze: "analyse",
    // Add more transformations as needed
  };

  let britishText = text;
  for (const [american, british] of Object.entries(americanToBritish)) {
    britishText = britishText.replace(new RegExp(american, "gi"), british);
  }
  return britishText;
};

class TranslationManager {
  constructor() {
    this.data = LANGUAGES;
    this.sources = {};
    this.tasks = {};
    this.masterTree = {};
  }

  initialize() {
    // Initialize data structure for each language
    this.data.forEach((lang) => {
      lang.texts = {};
      lang.modified = false;
    });

    this.sources = {};
    this.tasks = {};

    this.loadResources(() => {
      this.prepareTasks();
      this.executeTasks(() => {
        this.dumpResources(() => {
          console.log("✅ Translation completed successfully!");
        });
      });
    });
  }

  loadResources(callback, langIndex = 0) {
    const filePath = path.join(MESSAGES_DIR, `${this.data[langIndex].lang}.json`);

    fs.readFile(filePath, "utf8", (err, content) => {
      if (!err) {
        const translations = JSON.parse(content);

        if (langIndex === MASTER_INDEX) {
          this.masterTree = JSON.parse(JSON.stringify(translations));
        }

        this.prepareData(langIndex, translations);

        // Store source texts from master language
        if (langIndex === MASTER_INDEX) {
          for (const text in this.data[langIndex].texts) {
            this.sources[text] = langIndex;
          }
        }
      } else {
        console.error(`Error loading ${this.data[langIndex].lang}:`, err);
      }

      if (langIndex === this.data.length - 1) {
        return callback();
      }

      this.loadResources(callback, ++langIndex);
    });
  }

  prepareData(langIndex, obj, position = "") {
    for (const key in obj) {
      if (typeof obj[key] === "object") {
        this.prepareData(langIndex, obj[key], position + key + ".");
      } else {
        this.data[langIndex].texts[position + key] = obj[key];
      }
    }
  }

  prepareTasks() {
    for (let langIndex = 0; langIndex < this.data.length; langIndex++) {
      if (langIndex === MASTER_INDEX) continue;

      // Initialize task for this language
      this.tasks[langIndex] = {
        from: MASTER_INDEX,
        to: langIndex,
        texts: [],
        keys: [],
      };

      // Find missing translations
      for (const key in this.sources) {
        if (!this.data[langIndex].texts[key]) {
          this.data[langIndex].modified = true;
          this.tasks[langIndex].texts.push(this.data[MASTER_INDEX].texts[key]);
          this.tasks[langIndex].keys.push(key);
        }
      }
    }
  }

  executeTasks(callback) {
    const tasksList = Object.values(this.tasks);
    let completedTasks = 0;

    tasksList.forEach((task) => {
      this.executeTask(task, () => {
        completedTasks++;
        if (completedTasks === tasksList.length) {
          callback();
        }
      });
    });

    // If no tasks, call callback immediately
    if (tasksList.length === 0) {
      callback();
    }
  }

  executeTask(task, callback) {
    if (task.texts.length === 0) {
      return callback();
    }

    this.translateArray(
      task.texts,
      this.data[task.from].lang,
      this.data[task.to].lang,
      (translations) => {
        translations.forEach((translation, index) => {
          const key = task.keys[index];
          this.data[task.to].texts[key] = translation;
        });
        callback();
      }
    );
  }

  translateArray(texts, fromLang, toLang, callback) {
    if (texts.length === 0) {
      return callback([]);
    }

    // Special handling for British English
    if (toLang === "uk") {
      const britishTexts = texts.map((text) => applyBritishTransformations(text));
      return callback(britishTexts);
    }

    const minimizedArray = this.minimizeArrayForTranslation(texts);
    const results = [];

    this.callTranslateApi(fromLang, toLang, Object.values(minimizedArray), 0, results, callback);
  }

  callTranslateApi(fromLang, toLang, batches, batchIndex, results, callback) {
    const fromCode = getTranslatorCode(fromLang);
    const toCode = getTranslatorCode(toLang);

    request.post(
      {
        url: "http://api.microsofttranslator.com/V2/Http.svc/TranslateArray",
        headers: {
          "Content-Type": "application/json",
          "Ocp-Apim-Subscription-Key": "f4382a6fbb9f4586a224f6dcb390319e",
        },
        json: {
          From: fromCode,
          To: toCode,
          Texts: batches[batchIndex],
          AppId: "",
        },
      },
      (err, res, body) => {
        if (err) {
          console.error(`Translation error (${fromCode} -> ${toCode}):`, err);
          results.push(...batches[batchIndex]);
          if (++batchIndex === batches.length) {
            return callback(results);
          }
          return this.callTranslateApi(fromLang, toLang, batches, batchIndex, results, callback);
        }

        parseString(body, (parseErr, result) => {
          try {
            if (
              result &&
              result.ArrayOfTranslateArrayResponse &&
              result.ArrayOfTranslateArrayResponse.TranslateArrayResponse
            ) {
              const translations = result.ArrayOfTranslateArrayResponse.TranslateArrayResponse.map(
                (x) => x.TranslatedText[0]
              );

              // Post-process translations if needed
              const processedTranslations = translations.map((text) => {
                if (toLang === "uk") {
                  return applyBritishTransformations(text);
                }
                return text;
              });

              results.push(...processedTranslations);
            } else {
              console.log(`Unexpected API response for ${toCode}:`, body);
              results.push(...batches[batchIndex]);
            }
          } catch (e) {
            console.error(`Translation parsing error for ${toCode}:`, e);
            results.push(...batches[batchIndex]);
          }

          if (++batchIndex === batches.length) {
            return callback(results);
          }

          // Add delay between batches to avoid rate limiting
          setTimeout(() => {
            this.callTranslateApi(fromLang, toLang, batches, batchIndex, results, callback);
          }, 1000);
        });
      }
    );
  }

  minimizeArrayForTranslation(texts) {
    const batches = { 0: [] };
    let totalLength = 0;
    let batchIndex = 0;

    texts.forEach((text) => {
      totalLength += text.length;

      if (totalLength > 10000) {
        // Microsoft's character limit per request
        totalLength = text.length;
        batchIndex++;
        batches[batchIndex] = [text];
      } else {
        batches[batchIndex].push(text);
      }
    });

    return batches;
  }

  dumpResources(callback) {
    const modifiedLanguages = this.data.filter((x) => x.modified);
    let remainingWrites = modifiedLanguages.length;

    if (remainingWrites === 0) {
      return callback();
    }

    modifiedLanguages.forEach((lang) => {
      const translations = this.reconstructTranslations(lang.texts);
      const filePath = path.join(MESSAGES_DIR, `${lang.lang}.json`);

      fs.writeFile(filePath, JSON.stringify(translations, null, 2), "utf8", (err) => {
        if (err) {
          console.error(`Error saving ${lang.lang} translations:`, err);
        } else {
          console.log(`✅ Updated ${lang.lang} translations`);
        }

        remainingWrites--;
        if (remainingWrites === 0) {
          callback();
        }
      });
    });
  }

  reconstructTranslations(texts) {
    const result = {};

    for (const [key, value] of Object.entries(texts)) {
      let current = result;
      const parts = key.split(".");

      for (let i = 0; i < parts.length - 1; i++) {
        current[parts[i]] = current[parts[i]] || {};
        current = current[parts[i]];
      }

      current[parts[parts.length - 1]] = value;
    }

    return result;
  }
}

// Create and run the translator
const translator = new TranslationManager();
translator.initialize();
