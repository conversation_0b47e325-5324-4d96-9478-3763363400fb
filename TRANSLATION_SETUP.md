# Translation Setup Guide

This guide explains how to set up and use the automated translation system for the SKIDOS website.

## Overview

The translation script has been updated to use Microsoft Azure Translator V3 API, which is the current and supported version. The old V2 API was deprecated and shut down in 2021.

## Prerequisites

1. **Azure Account**: You need an Azure account to create a Translator resource
2. **Azure Translator Resource**: Create a Translator resource in Azure Portal

## Setup Instructions

### 1. Create Azure Translator Resource

1. Go to [Azure Portal](https://portal.azure.com/)
2. Click "Create a resource"
3. Search for "Translator" and select "Translator"
4. Fill in the required information:
   - **Subscription**: Your Azure subscription
   - **Resource Group**: Create new or use existing
   - **Region**: Choose a region close to you
   - **Name**: Give your resource a unique name
   - **Pricing Tier**: Choose based on your needs (F0 for free tier)
5. Click "Review + Create" then "Create"

### 2. Get Your API Key

1. Once the resource is created, go to your Translator resource
2. In the left menu, click "Keys and Endpoint"
3. Copy one of the keys (Key 1 or Key 2)

### 3. Set Environment Variable

#### Windows (Command Prompt)
```cmd
set AZURE_TRANSLATOR_KEY=your_api_key_here
```

#### Windows (PowerShell)
```powershell
$env:AZURE_TRANSLATOR_KEY="your_api_key_here"
```

#### Linux/Mac
```bash
export AZURE_TRANSLATOR_KEY=your_api_key_here
```

#### Using .env file (Recommended)
1. Copy `.env.example` to `.env`
2. Edit `.env` and add your API key:
```
AZURE_TRANSLATOR_KEY=your_api_key_here
```

## Usage

Once you have set up your API key, you can run the translation script:

```bash
pnpm translate
```

## How It Works

1. **Master Language**: English (`en.json`) is the master language file
2. **Target Languages**: The script translates to:
   - British English (`uk.json`) - Uses text transformations
   - Danish (`da.json`)
   - Portuguese/Brazilian (`br.json`)
   - Norwegian (`nb.json`)
   - Swedish (`sv.json`)

3. **Process**:
   - Reads all language files from `messages/` directory
   - Compares target languages with master language
   - Identifies missing translations
   - Calls Azure Translator API for missing translations
   - Updates the target language files

## Features

- **Rate Limiting**: Automatic delays between API calls to avoid rate limits
- **Error Handling**: Graceful error handling with fallback to original text
- **British English**: Special handling for British English with American-to-British transformations
- **Batch Processing**: Efficient batch processing for multiple texts
- **Character Limits**: Respects Azure API character limits per request

## Troubleshooting

### "AZURE_TRANSLATOR_KEY environment variable is required"
- Make sure you have set the environment variable correctly
- If using .env file, ensure it's in the project root directory

### "HTTP 401: Unauthorized"
- Check that your API key is correct
- Verify that your Azure Translator resource is active

### "HTTP 403: Forbidden"
- You may have exceeded your quota
- Check your Azure Translator resource usage in Azure Portal

### Network Errors
- Check your internet connection
- Verify that your firewall allows HTTPS connections to Azure

## Cost Considerations

- Azure Translator charges per character translated
- Free tier includes 2M characters per month
- Monitor your usage in Azure Portal to avoid unexpected charges

## Security Notes

- Never commit your API key to version control
- Use environment variables or .env files (add .env to .gitignore)
- Rotate your API keys regularly for security
